// البحث عن جميع التسجيلات للطالب - Multiple Registrations
const userId = $('Telegram Trigger').first().json.message.from.id.toString();
const chatId = $('Telegram Trigger').first().json.message.chat.id;

// تحديد مصدر البيانات (Google Sheets أو CSV)
let examData;
let isCSV = false;

// التحقق من نوع البيانات
if ($input.first().json && $input.first().json.data) {
  // CSV data
  isCSV = true;
  const csvData = $input.first().json.data || $input.first().binary.data;
  let csvText;
  
  if (typeof csvData === 'string') {
    csvText = csvData;
  } else {
    csvText = Buffer.from(csvData, 'base64').toString('utf8');
  }
  
  // تحويل CSV إلى مصفوفة
  const lines = csvText.split('\n').filter(line => line.trim());
  examData = lines.map(line => {
    const columns = line.split(',').map(col => col.trim().replace(/"/g, ''));
    return {
      json: {
        userId: columns[0] || '',
        name: columns[1] || '',
        examNumber: columns[2] || ''
      }
    };
  });
} else {
  // Google Sheets data
  examData = $input.all();
}

console.log('🔍 User ID المطلوب:', userId);
console.log('📊 عدد الصفوف:', examData.length);
console.log('📊 نوع البيانات:', isCSV ? 'CSV' : 'Google Sheets');

let allRegistrations = []; // مصفوفة لحفظ جميع التسجيلات

// البحث في جميع البيانات
for (let i = 0; i < examData.length; i++) {
  const item = examData[i];
  
  if (item && item.json) {
    const data = item.json;
    
    let rowUserId, studentName, examNumber;
    
    if (isCSV) {
      // بيانات CSV
      rowUserId = data.userId;
      studentName = data.name;
      examNumber = data.examNumber;
    } else {
      // بيانات Google Sheets
      // الطريقة 1: استخدام A, B, C
      rowUserId = data.A || data['A'];
      studentName = data.B || data['B'];
      examNumber = data.C || data['C'];
      
      // الطريقة 2: استخدام أول ثلاث قيم
      if (!rowUserId) {
        const values = Object.values(data);
        if (values.length >= 3) {
          rowUserId = values[0];
          studentName = values[1];
          examNumber = values[2];
        }
      }
    }
    
    console.log(`🔍 الصف ${i}:`, { rowUserId, studentName, examNumber });
    
    // التحقق من تطابق User ID
    if (rowUserId && rowUserId.toString() === userId) {
      allRegistrations.push({
        name: studentName || 'غير محدد',
        examNumber: examNumber || 'غير محدد',
        rowIndex: i + 1
      });
      console.log(`✅ تم العثور على تسجيل ${allRegistrations.length}:`, {
        name: studentName,
        examNumber: examNumber
      });
      // لا نتوقف هنا، نستمر في البحث عن المزيد
    }
  }
}

if (allRegistrations.length > 0) {
  // تكوين رسالة تحتوي على جميع التسجيلات
  let message = `📋 تسجيلاتك (${allRegistrations.length}):\n\n`;
  
  allRegistrations.forEach((registration, index) => {
    message += `${index + 1}. ${registration.name} - رقم امتحاني: ${registration.examNumber}\n`;
  });
  
  message += `\n🎓 بالتوفيق في جميع الامتحانات!`;
  
  console.log(`📋 تم العثور على ${allRegistrations.length} تسجيل(ات) للمستخدم ${userId}`);
  
  return [{
    json: {
      found: true,
      chatId: chatId,
      registrationsCount: allRegistrations.length,
      registrations: allRegistrations,
      message: message
    }
  }];
} else {
  // جمع User IDs للتشخيص
  const foundIds = [];
  examData.forEach((item, index) => {
    if (item && item.json) {
      const data = item.json;
      let firstValue;
      
      if (isCSV) {
        firstValue = data.userId;
      } else {
        firstValue = data.A || data['A'] || Object.values(data)[0];
      }
      
      if (firstValue) foundIds.push(firstValue);
    }
  });
  
  return [{
    json: {
      found: false,
      chatId: chatId,
      registrationsCount: 0,
      message: `🤝 أهلا وسهلا بك!\n\n📝 يبدو أنك لم تسجل بعد في النظام\n\n🎯 للحصول على رقمك الامتحاني، تحتاج أولاً للتسجيل:\n\n📋 **خطوات التسجيل:**\n1️⃣ اكتب: **تسجيل** + اسمك الكامل\n2️⃣ مثال: **تسجيل فاطمة قاسم علي**\n3️⃣ انتظر رسالة التأكيد\n4️⃣ بعدها اكتب: **رقمي** للحصول على رقمك\n\n📹 **شاهد فيديو الشرح:**\n<a href="https://t.me/saddis_iq/52">فيديو شرح التسجيل</a>\n\n💡 **ملاحظة:** بعد التسجيل ستحصل على رقم امتحاني (مثل: A595) يمكنك استخدامه لمشاهدة درجاتك\n\n🚀 ابدأ الآن بكتابة: **تسجيل** + اسمك`
    }
  }];
}
