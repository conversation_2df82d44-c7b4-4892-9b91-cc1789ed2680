{
  "name": "raad_autjoinbot",
  "nodes": [
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "exam-number-request-check",
              "leftValue": "={{$json.message.text}}",
              "rightValue": "رقمي",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        }
      },
      "id": "fa7401ea-0af6-4825-b821-181b6395f5cb",
      "name": "Check Exam Number Request",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        240,
        0
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "exam-number-regex",
              "leftValue": "={{$json.message.text}}",
              "rightValue": "^[A-Za-z]\\d+$",
              "operator": {
                "type": "string",
                "operation": "regex"
              }
            }
          ],
          "combinator": "and"
        }
      },
      "id": "exam-number-pattern-check",
      "name": "Check Exam Number Pattern",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        240,
        -160
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "register-check",
              "leftValue": "={{$json.message.text}}",
              "rightValue": "تسجيل",
              "operator": {
                "type": "string",
                "operation": "startsWith"
              }
            },
            {
              "id": "help-check",
              "leftValue": "={{$json.message.text}}",
              "rightValue": "ساعدني",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "or"
        }
      },
      "id": "new-register-help-check",
      "name": "Check Register or Help Command",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        240,
        320
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "exam-number-command-check",
              "leftValue": "={{$json.message.text}}",
              "rightValue": "رقمي",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        }
      },
      "id": "8a06ef2a-42f0-4fe3-ac55-b6ceebe7fcd2",
      "name": "Check Exam Number Command",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        240,
        160
      ]
    },
    {
      "parameters": {
        "url": "https://docs.google.com/spreadsheets/d/1GydiLM0iwiT1JHHdyeM8buB-2jeK9rK3_rDOupiTRes/export?format=csv&gid=0",
        "options": {}
      },
      "id": "9f49a441-dc8c-4be0-b2ca-0b6cdc0a0be0",
      "name": "Get Exam Numbers Sheet",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        440,
        180
      ]
    },
    {
      "parameters": {
        "jsCode": "// البحث عن جميع التسجيلات للطالب - Multiple Registrations\nconst userId = $('Telegram Trigger').first().json.message.from.id.toString();\nconst chatId = $('Telegram Trigger').first().json.message.chat.id;\n\n// تحديد مصدر البيانات (Google Sheets أو CSV)\nlet examData;\nlet isCSV = false;\n\n// التحقق من نوع البيانات\nif ($input.first().json && $input.first().json.data) {\n  // CSV data\n  isCSV = true;\n  const csvData = $input.first().json.data || $input.first().binary.data;\n  let csvText;\n  \n  if (typeof csvData === 'string') {\n    csvText = csvData;\n  } else {\n    csvText = Buffer.from(csvData, 'base64').toString('utf8');\n  }\n  \n  // تحويل CSV إلى مصفوفة\n  const lines = csvText.split('\\n').filter(line => line.trim());\n  examData = lines.map(line => {\n    const columns = line.split(',').map(col => col.trim().replace(/\"/g, ''));\n    return {\n      json: {\n        userId: columns[0] || '',\n        name: columns[1] || '',\n        examNumber: columns[2] || ''\n      }\n    };\n  });\n} else {\n  // Google Sheets data\n  examData = $input.all();\n}\n\nconsole.log('� User ID المطلوب:', userId);\nconsole.log('�📊 عدد الصفوف:', examData.length);\nconsole.log('📊 نوع البيانات:', isCSV ? 'CSV' : 'Google Sheets');\n\nlet allRegistrations = []; // مصفوفة لحفظ جميع التسجيلات\n\n// البحث في جميع البيانات\nfor (let i = 0; i < examData.length; i++) {\n  const item = examData[i];\n  \n  if (item && item.json) {\n    const data = item.json;\n    \n    let rowUserId, studentName, examNumber;\n    \n    if (isCSV) {\n      // بيانات CSV\n      rowUserId = data.userId;\n      studentName = data.name;\n      examNumber = data.examNumber;\n    } else {\n      // بيانات Google Sheets\n      // الطريقة 1: استخدام A, B, C\n      rowUserId = data.A || data['A'];\n      studentName = data.B || data['B'];\n      examNumber = data.C || data['C'];\n      \n      // الطريقة 2: استخدام أول ثلاث قيم\n      if (!rowUserId) {\n        const values = Object.values(data);\n        if (values.length >= 3) {\n          rowUserId = values[0];\n          studentName = values[1];\n          examNumber = values[2];\n        }\n      }\n    }\n    \n    console.log(`🔍 الصف ${i}:`, { rowUserId, studentName, examNumber });\n    \n    // التحقق من تطابق User ID\n    if (rowUserId && rowUserId.toString() === userId) {\n      allRegistrations.push({\n        name: studentName || 'غير محدد',\n        examNumber: examNumber || 'غير محدد',\n        rowIndex: i + 1\n      });\n      console.log(`✅ تم العثور على تسجيل ${allRegistrations.length}:`, {\n        name: studentName,\n        examNumber: examNumber\n      });\n      // لا نتوقف هنا، نستمر في البحث عن المزيد\n    }\n  }\n}\n\nif (allRegistrations.length > 0) {\n  // تكوين رسالة تحتوي على جميع التسجيلات\n  let message = `📋 تسجيلاتك (${allRegistrations.length}):\\n\\n`;\n  \n  allRegistrations.forEach((registration, index) => {\n    message += `${index + 1}. ${registration.name} - رقم امتحاني: ${registration.examNumber}\\n`;\n  });\n  \n  message += `\\n🎓 بالتوفيق في جميع الامتحانات!`;\n  \n  console.log(`📋 تم العثور على ${allRegistrations.length} تسجيل(ات) للمستخدم ${userId}`);\n  \n  return [{\n    json: {\n      found: true,\n      chatId: chatId,\n      registrationsCount: allRegistrations.length,\n      registrations: allRegistrations,\n      message: message\n    }\n  }];\n} else {\n  // جمع User IDs للتشخيص\n  const foundIds = [];\n  examData.forEach((item, index) => {\n    if (item && item.json) {\n      const data = item.json;\n      let firstValue;\n      \n      if (isCSV) {\n        firstValue = data.userId;\n      } else {\n        firstValue = data.A || data['A'] || Object.values(data)[0];\n      }\n      \n      if (firstValue) foundIds.push(firstValue);\n    }\n  });\n  \n  return [{\n    json: {\n      found: false,\n      chatId: chatId,\n      registrationsCount: 0,\n      message: `🤝 أهلا وسهلا بك!\\n\\n📝 يبدو أنك لم تسجل بعد في النظام\\n\\n🎯 للحصول على رقمك الامتحاني، تحتاج أولاً للتسجيل:\\n\\n📋 **خطوات التسجيل:**\\n1️⃣ اكتب: **تسجيل** + اسمك الكامل\\n2️⃣ مثال: **تسجيل فاطمة قاسم علي**\\n3️⃣ انتظر رسالة التأكيد\\n4️⃣ بعدها اكتب: **رقمي** للحصول على رقمك\\n\\n📹 **شاهد فيديو الشرح:**\\n<a href=\\"https://t.me/saddis_iq/52\\">فيديو شرح التسجيل</a>\\n\\n� **ملاحظة:** بعد التسجيل ستحصل على رقم امتحاني (مثل: A595) يمكنك استخدامه لمشاهدة درجاتك\\n\\n🚀 ابدأ الآن بكتابة: **تسجيل** + اسمك`\n    }\n  }];\n}"
      },
      "id": "b0fce6ed-fbd7-4954-9f50-9b2dacf34124",
      "name": "Search Exam Number",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        700,
        160
      ]
    },
    {
      "parameters": {
        "conditions": {
          "boolean": [
            {
              "value1": "={{$json.found}}",
              "value2": true
            }
          ]
        }
      },
      "id": "a4916ff4-14eb-48f3-926a-8c82bc2122f2",
      "name": "Check If Found",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        940,
        160
      ]
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {}
      },
      "id": "9da3e14d-80f9-4777-9ffb-18c8004c63f7",
      "name": "Send Exam Number",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        1180,
        80
      ],
      "webhookId": "exam-number-webhook",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {}
      },
      "id": "79c57e04-9d9f-41f3-bfbb-47db8a128c3f",
      "name": "Send Not Found Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        1180,
        240
      ],
      "webhookId": "not-found-webhook",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// استخراج اسم الطالب من الرسالة\nconst messageText = $input.first().json.message.text;\nconst studentName = messageText.replace('تسجيل', '').trim();\nconst chatId = $input.first().json.message.chat.id;\nconst userId = $input.first().json.message.from.id;\nconst username = $input.first().json.message.from.username || 'غير محدد';\n\n// التحقق من وجود اسم\nif (!studentName) {\n  return [{\n    json: {\n      error: true,\n      message: 'يرجى إدخال اسم الطالب بعد كلمة تسجيل\\nمثال: تسجيل أحمد محمد',\n      chatId: chatId\n    }\n  }];\n}\n\n// إعداد البيانات للإرسال إلى Google Sheets\nreturn [{\n  json: {\n    studentName: studentName,\n    chatId: chatId,\n    userId: userId,\n    username: username,\n    registrationDate: new Date().toISOString(),\n    registrationTime: new Date().toLocaleString('en-US', {\n      timeZone: 'Africa/Cairo'\n    })\n  }\n}];"
      },
      "id": "548181f5-c6db-4ecc-befb-a71f850cc489",
      "name": "Extract Student Name",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        460,
        580
      ]
    },
    {
      "parameters": {
        "conditions": {
          "boolean": [
            {
              "value1": "={{$json.error}}",
              "value2": true
            }
          ]
        }
      },
      "id": "11ccfdae-b63b-4075-a4f9-f62f2744bdd4",
      "name": "Check for Errors",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        700,
        560
      ]
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {}
      },
      "id": "0c311920-6871-49b8-9974-6c5052af69a5",
      "name": "Send Error Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        800,
        240
      ],
      "webhookId": "d9dffe5c-d143-4373-b9ef-568d24625e1b",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "documentId": {
          "__rl": true,
          "value": "1Rt9ZlR0Oq0As3-OTM9jNFifwdnBqmE3YOl4E6vqT5hM",
          "mode": "id"
        },
        "sheetName": {
          "__rl": true,
          "value": "Students",
          "mode": "name"
        },
        "options": {}
      },
      "id": "efe312c4-7c5f-4e4a-9700-2e4b46cffefc",
      "name": "Check for Duplicate Names",
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 4,
      "position": [
        920,
        580
      ],
      "credentials": {
        "googleSheetsOAuth2Api": {
          "id": "6x2hRiWG966rXRWo",
          "name": "Google Sheets account"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// التحقق من وجود اسم مكرر\nconst currentStudentName = $('Extract Student Name').first().json.studentName;\nconst existingStudents = $input.all();\n\n// التحقق من صحة البيانات\nif (!currentStudentName) {\n  return [{\n    json: {\n      isDuplicate: false,\n      error: true,\n      message: 'خطأ: لم يتم العثور على اسم الطالب'\n    }\n  }];\n}\n\n// البحث عن اسم مطابق مع تنظيف البيانات\nconst normalizedCurrentName = currentStudentName.toLowerCase().trim();\nconst isDuplicate = existingStudents.some(student => {\n  if (!student.json || !student.json.studentName) return false;\n  const normalizedExistingName = student.json.studentName.toLowerCase().trim();\n  return normalizedExistingName === normalizedCurrentName;\n});\n\n// إعداد البيانات المشتركة\nconst baseData = {\n  chatId: $('Extract Student Name').first().json.chatId,\n  studentName: currentStudentName\n};\n\nif (isDuplicate) {\n  return [{\n    json: {\n      ...baseData,\n      isDuplicate: true,\n      message: `❌📝 \"${currentStudentName}\" قد تم تسجيلك مسبقاً بالفعل\\n\\n📺 يرجى متابعة قناة الأستاذ رعد @Raad555 بينما تبدأ الدورة 🎓`\n    }\n  }];\n} else {\n  return [{\n    json: {\n      ...baseData,\n      isDuplicate: false\n    }\n  }];\n}"
      },
      "id": "b72426b3-01cf-4de6-9fb2-a0d6b47d61a4",
      "name": "Process Duplicate Check",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1100,
        580
      ]
    },
    {
      "parameters": {
        "conditions": {
          "boolean": [
            {
              "value1": "={{$json.isDuplicate}}",
              "value2": true
            }
          ]
        }
      },
      "id": "0fb9a298-cecb-48fc-8452-74a4c7ad094e",
      "name": "Check If Duplicate",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        1260,
        580
      ]
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {}
      },
      "id": "cce686c6-dad6-4035-812a-cf635bb2c1bb",
      "name": "Send Duplicate Error",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        1400,
        240
      ],
      "webhookId": "duplicate-error-webhook",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "operation": "append",
        "documentId": {
          "__rl": true,
          "value": "1Rt9ZlR0Oq0As3-OTM9jNFifwdnBqmE3YOl4E6vqT5hM",
          "mode": "id"
        },
        "sheetName": {
          "__rl": true,
          "value": "Students",
          "mode": "name"
        },
        "columns": {
          "mappingMode": "autoMapInputData",
          "value": {
            "اسم الطالب": "={{ $json.studentName }}",
            "هل هو مكرر": "={{ $json.isDuplicate }}",
            "ايدي الطالب": "={{ $json.chatId }}"
          },
          "matchingColumns": [],
          "schema": [
            {
              "id": "اسم الطالب",
              "displayName": "اسم الطالب",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true
            },
            {
              "id": "ايدي الطالب",
              "displayName": "ايدي الطالب",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            },
            {
              "id": "هل هو مكرر",
              "displayName": "هل هو مكرر",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            },
            {
              "id": "chatId",
              "displayName": "chatId",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            },
            {
              "id": "userId",
              "displayName": "userId",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            },
            {
              "id": "username",
              "displayName": "username",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            },
            {
              "id": "registrationDate",
              "displayName": "registrationDate",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            },
            {
              "id": "registrationTime",
              "displayName": "registrationTime",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            },
            {
              "id": "isDuplicate",
              "displayName": "isDuplicate",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            },
            {
              "id": "studentName",
              "displayName": "studentName",
              "required": false,
              "defaultMatch": false,
              "display": true,
              "type": "string",
              "canBeUsedToMatch": true,
              "removed": false
            }
          ],
          "attemptToConvertTypes": false,
          "convertFieldsToString": false
        },
        "options": {}
      },
      "id": "888b8245-37c5-42d3-8830-10e55a5862d1",
      "name": "Add to Google Sheets",
      "type": "n8n-nodes-base.googleSheets",
      "typeVersion": 4,
      "position": [
        1500,
        580
      ],
      "credentials": {
        "googleSheetsOAuth2Api": {
          "id": "6x2hRiWG966rXRWo",
          "name": "Google Sheets account"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// استرجاع البيانات من العقدة السابقة\nconst extractData = $('Extract Student Name').first().json;\n\n// إرجاع البيانات بشكل واضح\nreturn [{\n  json: {\n    chatId: extractData.chatId,\n    studentName: extractData.studentName,\n    registrationTime: extractData.registrationTime,\n    message: `🎉 تم تسجيل الطالب بنجاح! ✅\n\n👤 اسم الطالب: ${extractData.studentName}\n📅 تاريخ التسجيل: ${extractData.registrationTime}\n\n📢 والان تابع قناة الاستاذ رعد: @Raad555\n⏳ وانتظر في الايام القادمة نشر القائمة التي تحتوي على رقمك الامتحاني لاستخدامه في الامتحانات 📝`\n  }\n}];"
      },
      "id": "0e2521de-558f-4d6c-864d-4c06d055bd29",
      "name": "Prepare Success Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1600,
        580
      ]
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {}
      },
      "id": "a02294a8-7676-443b-844d-aa67d78cfada",
      "name": "Send Success Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        1720,
        580
      ],
      "webhookId": "5b048772-c95c-4c35-ac73-f0820198f257",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{$json.message.chat.id}}",
        "text": "🆘 تحتاج مساعدة؟\n\n📹 شاهد فيديو شرح طريقة التسجيل:\n<a href=\"https://t.me/saddis_iq/52\">فيديو شرح التسجيل</a>\n\n👨‍💻 تواصل مع مبرمج البوت:\n<a href=\"https://t.me/A_5_6\">@A_5_6</a>\n\n📞 سيقوم بمساعدتك في حل أي مشكلة تواجهها! 🤝",
        "additionalFields": {
          "parse_mode": "HTML"
        }
      },
      "id": "cd651249-a559-41c0-8d16-b72a19350d3f",
      "name": "Send Help Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        440,
        20
      ],
      "webhookId": "4f14410c-c3c7-460c-843d-1d62d7ea5433",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "help-command-check",
              "leftValue": "={{$json.message.text}}",
              "rightValue": "ساعدني",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        }
      },
      "id": "c3b9d868-7f71-49ff-a34d-759ec43255d2",
      "name": "Check Help Command",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        240,
        300
      ]
    },
    {
      "parameters": {
        "chatId": "={{$json.message.chat.id}}",
        "text": "تحتاج مساعدة؟\\n\\nالأوامر المتاحة:\\n\\n1- للتسجيل:\\n• اكتب تسجيل + اسم الطالب\\n• مثال: تسجيل رعد ابراهيم\\n\\n2- للحصول على رقمك الامتحاني:\\n• اكتب رقمي\\n\\n3- لمشاهدة درجاتك:\\n• أرسل رقمك الامتحاني مباشرة\\n• مثال: A595, B123, C456\\n\\nشاهد فيديو شرح طريقة التسجيل:\\nhttps://t.me/saddis_iq/52\\n\\nتواصل مع مبرمج البوت:\\n@A_5_6\\n\\nسيقوم بمساعدتك في حل أي مشكلة تواجهها!",
        "additionalFields": {
          "parse_mode": "HTML"
        }
      },
      "id": "5b0c614a-5c24-4f70-8b1c-a55d32a53230",
      "name": "Send Support Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        460,
        320
      ],
      "webhookId": "support-message-webhook",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "updates": [
          "message"
        ],
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.2,
      "position": [
        0,
        0
      ],
      "id": "ad115aea-8f46-4f83-9d97-96bce282e560",
      "name": "Telegram Trigger",
      "webhookId": "e0abaf07-1937-460e-ab8d-c364ffb3530d",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "url": "https://docs.google.com/spreadsheets/d/1GydiLM0iwiT1JHHdyeM8buB-2jeK9rK3_rDOupiTRes/export?format=csv&gid=0",
        "options": {}
      },
      "id": "get-grades-sheet",
      "name": "Get Grades Sheet",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        440,
        -160
      ]
    },
    {
      "parameters": {
        "jsCode": "// البحث عن الرقم الامتحاني واستخراج الدرجات - نفس منطق أمر رقمي\nconst examNumber = $('Telegram Trigger').first().json.message.text.trim().toUpperCase();\nconst chatId = $('Telegram Trigger').first().json.message.chat.id;\n\n// تحديد مصدر البيانات (Google Sheets أو CSV) - نفس منطق Search Exam Number\nlet gradesData;\nlet isCSV = false;\n\n// التحقق من نوع البيانات - نفس الطريقة المستخدمة في أمر رقمي\nif ($input.first().json && $input.first().json.data) {\n  // CSV data\n  isCSV = true;\n  const csvData = $input.first().json.data || $input.first().binary.data;\n  let csvText;\n  \n  if (typeof csvData === 'string') {\n    csvText = csvData;\n  } else {\n    csvText = Buffer.from(csvData, 'base64').toString('utf8');\n  }\n  \n  // تحويل CSV إلى مصفوفة - نفس الطريقة\n  const lines = csvText.split('\\n').filter(line => line.trim());\n  gradesData = lines.map(line => {\n    const columns = line.split(',').map(col => col.trim().replace(/\"/g, ''));\n    return {\n      json: {\n        userId: columns[0] || '',\n        name: columns[1] || '',\n        examNumber: columns[2] || '',\n        grade1: columns[3] || '',\n        grade2: columns[4] || '',\n        grade3: columns[5] || '',\n        grade4: columns[6] || '',\n        grade5: columns[7] || '',\n        grade6: columns[8] || '',\n        grade7: columns[9] || '',\n        grade8: columns[10] || '',\n        grade9: columns[11] || '',\n        grade10: columns[12] || ''\n      }\n    };\n  });\n} else {\n  // Google Sheets data\n  gradesData = $input.all();\n}\n\nconsole.log('🔍 البحث عن الرقم الامتحاني:', examNumber);\nconsole.log('📊 عدد الصفوف:', gradesData.length);\nconsole.log('📊 نوع البيانات:', isCSV ? 'CSV' : 'Google Sheets');\n\nlet studentFound = null;\n\n// البحث في جميع البيانات - نفس منطق Search Exam Number\nfor (let i = 0; i < gradesData.length; i++) {\n  const item = gradesData[i];\n  \n  if (item && item.json) {\n    const data = item.json;\n    \n    let studentName, studentExamNumber;\n    \n    if (isCSV) {\n      // بيانات CSV - نفس الطريقة المستخدمة في أمر رقمي\n      studentName = data.name;\n      studentExamNumber = data.examNumber;\n    } else {\n      // بيانات Google Sheets - نفس الطريقة\n      studentName = data.B || data['B'];\n      studentExamNumber = data.C || data['C'];\n      \n      // إذا لم نجد البيانات، نحاول استخدام القيم بالترتيب\n      if (!studentExamNumber) {\n        const values = Object.values(data);\n        if (values.length >= 3) {\n          studentName = values[1]; // العمود الثاني\n          studentExamNumber = values[2]; // العمود الثالث\n        }\n      }\n    }\n    \n    console.log(`🔍 الصف ${i}:`, { studentName, studentExamNumber });\n    \n    // التحقق من تطابق الرقم الامتحاني - نفس الطريقة\n    if (studentExamNumber && studentExamNumber.toString().toUpperCase() === examNumber) {\n      // استخراج جميع الدرجات - نفس منطق أمر رقمي\n      const grades = [];\n      const gradeFields = ['grade1', 'grade2', 'grade3', 'grade4', 'grade5', 'grade6', 'grade7', 'grade8', 'grade9', 'grade10'];\n      \n      gradeFields.forEach((field, index) => {\n        let gradeValue;\n        \n        if (isCSV) {\n          gradeValue = data[field];\n        } else {\n          // للـ Google Sheets، نستخدم الأعمدة D, E, F, etc.\n          const columnLetter = String.fromCharCode(68 + index); // D=68, E=69, F=70...\n          gradeValue = data[columnLetter] || data[columnLetter.toLowerCase()];\n          \n          // إذا لم نجد البيانات، نحاول استخدام القيم بالترتيب\n          if (!gradeValue) {\n            const values = Object.values(data);\n            const columnIndex = 3 + index; // بدءاً من العمود الرابع (D)\n            if (values.length > columnIndex) {\n              gradeValue = values[columnIndex];\n            }\n          }\n        }\n        \n        // إضافة الدرجة إذا كانت موجودة وليست فارغة\n        if (gradeValue && gradeValue.toString().trim() !== '') {\n          grades.push({\n            examTitle: `الامتحان ${getArabicNumber(index + 1)}`,\n            grade: gradeValue.toString().trim()\n          });\n        }\n      });\n      \n      studentFound = {\n        name: studentName || 'غير محدد',\n        examNumber: studentExamNumber,\n        grades: grades,\n        rowIndex: i + 1\n      };\n      \n      console.log(`✅ تم العثور على الطالب:`, studentFound);\n      break;\n    }\n  }\n}\n\n// دالة لتحويل الأرقام إلى العربية\nfunction getArabicNumber(num) {\n  const arabicNumbers = {\n    1: 'الأول',\n    2: 'الثاني', \n    3: 'الثالث',\n    4: 'الرابع',\n    5: 'الخامس',\n    6: 'السادس',\n    7: 'السابع',\n    8: 'الثامن',\n    9: 'التاسع',\n    10: 'العاشر',\n    11: 'الحادي عشر',\n    12: 'الثاني عشر'\n  };\n  \n  return arabicNumbers[num] || `رقم ${num}`;\n}\n\nif (studentFound) {\n  // تكوين رسالة الدرجات\n  let message = `📊 درجات الطالب:\\n\\n`;\n  message += `👤 الاسم: ${studentFound.name}\\n`;\n  message += `🔢 الرقم الامتحاني: ${studentFound.examNumber}\\n\\n`;\n  \n  if (studentFound.grades.length > 0) {\n    message += `📋 الدرجات:\\n`;\n    studentFound.grades.forEach((grade, index) => {\n      message += `${index + 1}. ${grade.examTitle}: ${grade.grade}\\n`;\n    });\n  } else {\n    message += `📋 لا توجد درجات مسجلة حتى الآن\\n`;\n  }\n  \n  message += `\\n🎓 بالتوفيق! 📚`;\n  \n  console.log(`📋 تم العثور على الطالب ${studentFound.name} برقم امتحاني ${studentFound.examNumber}`);\n  \n  return [{\n    json: {\n      found: true,\n      chatId: chatId,\n      studentName: studentFound.name,\n      examNumber: studentFound.examNumber,\n      gradesCount: studentFound.grades.length,\n      grades: studentFound.grades,\n      message: message\n    }\n  }];\n} else {\n  return [{\n    json: {\n      found: false,\n      chatId: chatId,\n      examNumber: examNumber,\n      message: `❌ لم يتم العثور على الرقم الامتحاني: ${examNumber}\\n\\n🔍 تأكد من صحة الرقم الامتحاني\\n📝 مثال صحيح: A595, B123, C456\\n\\n💡 للحصول على رقمك الامتحاني اكتب: رقمي`\n    }\n  }];\n}"
      },
      "id": "search-grades-by-exam-number",
      "name": "Search Grades by Exam Number",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        700,
        -160
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "grades-found-check",
              "leftValue": "={{$json.found}}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "true"
              }
            }
          ],
          "combinator": "and"
        }
      },
      "id": "check-if-grades-found",
      "name": "Check If Grades Found",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        940,
        -160
      ]
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {}
      },
      "id": "send-grades-message",
      "name": "Send Grades Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        1180,
        -240
      ],
      "webhookId": "grades-message-webhook",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{$json.chatId}}",
        "text": "={{$json.message}}",
        "additionalFields": {}
      },
      "id": "send-grades-not-found",
      "name": "Send Grades Not Found",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        1180,
        -80
      ],
      "webhookId": "grades-not-found-webhook",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    },
    {
      "parameters": {
        "chatId": "={{$('Telegram Trigger').first().json.message.chat.id}}",
        "text": "أهلا وسهلا 🤝\n\n🎯 اختار الي تحتاجه من الأوامر التالية:\n\n🆕 طالب جديد؟\n📝 اكتب: تسجيل + اسمك\n💡 مثال: تسجيل فاطمة قاسم\n\n🔍 تريد رقمك الامتحاني؟\n🔢 اكتب: رقمي\n\n📊 تريد مشاهدة درجاتك؟\n📋 أرسل رقمك الامتحاني مباشرة\n💡 مثال: A595 أو A23\n\n🆘 تحتاج مساعدة إضافية؟\n💬 اكتب: ساعدني",
        "additionalFields": {}
      },
      "id": "send-unknown-command-message",
      "name": "Send Unknown Command Message",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1,
      "position": [
        480,
        80
      ],
      "webhookId": "unknown-command-webhook",
      "credentials": {
        "telegramApi": {
          "id": "ZcOvETMMM5MwlD6B",
          "name": "Telegram account 8"
        }
      }
    }
  ],
  "pinData": {},
  "connections": {
    "Check Exam Number Request": {
      "main": [
        [
          {
            "node": "Check Exam Number Command",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Check Register or Help Command",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Register or Help Command": {
      "main": [
        [
          {
            "node": "Check Help Command",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send Unknown Command Message",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Exam Number Command": {
      "main": [
        [
          {
            "node": "Get Exam Numbers Sheet",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Exam Numbers Sheet": {
      "main": [
        [
          {
            "node": "Search Exam Number",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Search Exam Number": {
      "main": [
        [
          {
            "node": "Check If Found",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check If Found": {
      "main": [
        [
          {
            "node": "Send Exam Number",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send Not Found Message",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Help Command": {
      "main": [
        [
          {
            "node": "Send Help Message",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Extract Student Name",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract Student Name": {
      "main": [
        [
          {
            "node": "Check for Errors",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check for Errors": {
      "main": [
        [
          {
            "node": "Send Error Message",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Check for Duplicate Names",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check for Duplicate Names": {
      "main": [
        [
          {
            "node": "Process Duplicate Check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Process Duplicate Check": {
      "main": [
        [
          {
            "node": "Check If Duplicate",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check If Duplicate": {
      "main": [
        [
          {
            "node": "Send Duplicate Error",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Add to Google Sheets",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Add to Google Sheets": {
      "main": [
        [
          {
            "node": "Prepare Success Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Prepare Success Data": {
      "main": [
        [
          {
            "node": "Send Success Message",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Telegram Trigger": {
      "main": [
        [
          {
            "node": "Check Exam Number Pattern",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Exam Number Pattern": {
      "main": [
        [
          {
            "node": "Get Grades Sheet",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Check Exam Number Request",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Grades Sheet": {
      "main": [
        [
          {
            "node": "Search Grades by Exam Number",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Search Grades by Exam Number": {
      "main": [
        [
          {
            "node": "Check If Grades Found",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check If Grades Found": {
      "main": [
        [
          {
            "node": "Send Grades Message",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send Grades Not Found",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {
    "executionOrder": "v1"
  },
  "versionId": "1db244c5-33c0-4f17-a6c0-fe4ef32d34da",
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "dbe3f1f7c9346c54ba60a20755e42eba02e9994f9298e573ede21a7151dfc62e"
  },
  "id": "hWFTzkQoU05QKKVB",
  "tags": []
}